#!/usr/bin/env python
# -*- coding: utf-8 -*-

import pandas as pd
import re
import numpy as np

print("开始处理价格数据清洗...")

try:
    # 读取Excel文件
    file_path = "使用八爪鱼爬取的大众点评上有关昆山地段的奶茶店情况.xlsx"
    df = pd.read_excel(file_path)
    print(f"成功读取文件，数据形状: {df.shape}")

    # 显示价格列的一些样本数据
    print("\n价格列样本数据（清洗前）:")
    sample_prices = df['价格'].head(10).tolist()
    for i, price in enumerate(sample_prices):
        print(f"{i+1}. {repr(price)}")

    def extract_price(price_value):
        """从价格值中提取数字"""
        if pd.isna(price_value):
            return np.nan

        # 如果已经是数字，直接返回
        if isinstance(price_value, (int, float)):
            return int(price_value)

        # 如果是字符串，提取数字
        if isinstance(price_value, str):
            # 使用正则表达式提取数字
            numbers = re.findall(r'\d+', str(price_value))
            if numbers:
                # 取第一个数字（通常是价格）
                return int(numbers[0])

        return np.nan

    # 应用清洗函数
    print("\n正在清洗价格数据...")
    df['价格'] = df['价格'].apply(extract_price)

    print("\n价格列样本数据（清洗后）:")
    cleaned_prices = df['价格'].head(10).tolist()
    for i, price in enumerate(cleaned_prices):
        print(f"{i+1}. {price}")

    # 显示清洗结果统计
    print(f"\n清洗结果统计:")
    print(f"总记录数: {len(df)}")
    print(f"有效价格记录数: {df['价格'].notna().sum()}")
    print(f"缺失价格记录数: {df['价格'].isna().sum()}")

    valid_prices = df['价格'].dropna()
    if len(valid_prices) > 0:
        print(f"价格范围: {valid_prices.min()} - {valid_prices.max()}")
        print(f"平均价格: {valid_prices.mean():.2f}")

    # 保存清洗后的数据
    output_file = "使用八爪鱼爬取的大众点评上有关昆山地段的奶茶店情况_价格清洗.xlsx"
    df.to_excel(output_file, index=False)
    print(f"\n清洗后的数据已保存到: {output_file}")
    print("价格数据清洗完成！")

except Exception as e:
    print(f"处理文件时出错: {e}")
    import traceback
    traceback.print_exc()
